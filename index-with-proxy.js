import gplay from "google-play-scraper";
import { HttpsProxyAgent } from 'https-proxy-agent';

// 代理配置 - 请根据您的代理服务器设置修改
const PROXY_CONFIG = {
    // 示例代理配置，请替换为您的实际代理
    host: '127.0.0.1',
    port: 7897,
    // 如果需要认证，取消注释下面的行
    // username: 'your-username',
    // password: 'your-password'
};

console.log('开始请求 Google Play 数据（使用代理）...');

// 创建代理 agent
function createProxyAgent() {
    const { host, port, username, password } = PROXY_CONFIG;
    
    if (username && password) {
        return new HttpsProxyAgent(`http://${username}:${password}@${host}:${port}`);
    } else {
        return new HttpsProxyAgent(`http://${host}:${port}`);
    }
}

// 修改 google-play-scraper 的请求配置
// 注意：这需要修改库的内部配置，可能不是最佳方案
async function fetchWithProxy() {
    try {
        // 尝试使用代理
        const proxy = createProxyAgent();
        
        let scrapingOptions = {
            appId: data["appId"],
            requestOptions: {
                retry: { limit: 0 },
                agent: {
                    https: proxy,
                    http: proxy,
                    http2: proxy,
                },
            },
            throttle: 5,
        }
        // 这里我们需要检查 google-play-scraper 是否支持自定义 agent
        // 如果不支持，我们需要使用其他方法
        
        const apps = await gplay.list({scrapingOptions,
            category: gplay.category.GAME_ACTION,
            collection: gplay.collection.TOP_FREE,
            num: 2,
            country: 'us',
            lang: 'en'
        });
        
        console.log('成功获取应用列表:');
        console.log(`获取到 ${apps.length} 个应用`);
        apps.forEach((app, index) => {
            console.log(`${index + 1}. ${app.title} - ${app.developer}`);
            console.log(`   URL: ${app.url}`);
            console.log(`   评分: ${app.score}`);
            console.log('---');
        });
        
        return apps;
    } catch (err) {
        console.error('使用代理请求失败:', err.message);
        throw err;
    }
}

// 提供手动解决方案
function showManualSolutions() {
    console.log('\n=== 解决方案 ===');
    console.log('由于网络限制，无法直接访问 Google Play。以下是几种解决方案：');
    console.log('');
    console.log('1. 使用 VPN 服务:');
    console.log('   - 连接到支持访问 Google 服务的 VPN 服务器');
    console.log('   - 然后重新运行脚本');
    console.log('');
    console.log('2. 使用代理服务器:');
    console.log('   - 修改 index-with-proxy.js 中的 PROXY_CONFIG');
    console.log('   - 设置正确的代理服务器地址和端口');
    console.log('');
    console.log('3. 使用替代方案:');
    console.log('   - 考虑使用其他应用商店的 API');
    console.log('   - 或者使用预先收集的数据');
    console.log('');
    console.log('4. 环境变量代理设置:');
    console.log('   - 设置 HTTP_PROXY 和 HTTPS_PROXY 环境变量');
    console.log('   - 例如: export HTTPS_PROXY=http://proxy-server:port');
    console.log('');
    console.log('5. 修改系统代理设置:');
    console.log('   - 在系统网络设置中配置代理');
    console.log('   - Node.js 会自动使用系统代理设置');
}

// 检查是否配置了代理
if (PROXY_CONFIG.host === 'your-proxy-host.com') {
    console.log('⚠️  代理未配置，请先修改 PROXY_CONFIG');
    showManualSolutions();
} else {
    fetchWithProxy().catch(err => {
        console.error('请求失败:', err.message);
        showManualSolutions();
    });
}
